export async function logErrorToKV(env, error, additionalData = {}) {
  const errorId = crypto.randomUUID().replace(/-/g, '').toUpperCase().slice(0, 10);

  const expirationTtl = 7 * 60 * 60 * 24; // 7 days in seconds

  const errorData = {
    id: errorId,
    timestamp: new Date().toISOString(),
    name: error?.name || 'Error',
    message: error?.message || 'Unknown Error',
    stack: error?.stack,
    ...additionalData,
  };

  if (error?.cause) {
    errorData.cause = error.cause;
  }

  console.error('Error ID:', errorId);
  console.error(error?.message, error?.stack);

  await env.LOGS.put(`error:${errorId}`, JSON.stringify(errorData), { expirationTtl });

  return { errorId, errorData };
}

export async function logPandaDocRequestToKV(env, requestData, responseData, error = null) {
  const logId = crypto.randomUUID().replace(/-/g, '').toUpperCase().slice(0, 10);

  const expirationTtl = 30 * 60 * 60 * 24; // 30 days in seconds

  const logData = {
    id: logId,
    timestamp: new Date().toISOString(),
    request: {
      method: requestData.method,
      endpoint: requestData.endpoint,
      url: requestData.url,
      headers: requestData.headers,
      body: requestData.body,
    },
    response: responseData
      ? {
          status: responseData.status,
          statusText: responseData.statusText,
          headers: responseData.headers,
          body: responseData.body,
          contentType: responseData.contentType,
        }
      : null,
    error: error
      ? {
          message: error.message,
          cause: error.cause,
        }
      : null,
    success: !error,
  };

  await env.LOGS.put(`pandadoc:${logId}`, JSON.stringify(logData), { expirationTtl });

  return { logId, logData };
}

export async function getPandaDocLogs(env, limit = 50) {
  const logs = [];
  const listResult = await env.LOGS.list({ prefix: 'pandadoc:', limit });

  for (const key of listResult.keys) {
    const logData = await env.LOGS.get(key.name, { type: 'json' });
    if (logData) {
      logs.push(logData);
    }
  }

  // Sort by timestamp descending (newest first)
  logs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

  return logs;
}
