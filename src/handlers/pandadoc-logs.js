import { createFactory } from 'hono/factory';
import { getPandaDocLogs } from '../kv/logs.js';

const factory = createFactory();

export const pandadocLogsHandlers = factory.createHandlers(async (c) => {
  const limit = parseInt(c.req.query('limit')) || 50;
  const maxLimit = 200;
  
  const actualLimit = Math.min(limit, maxLimit);
  
  const logs = await getPandaDocLogs(c.env, actualLimit);
  
  return c.json({
    logs,
    count: logs.length,
    limit: actualLimit,
  });
});
