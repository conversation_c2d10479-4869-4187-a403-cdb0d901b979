import { SELF } from 'cloudflare:test';
import { describe, expect, it } from 'vitest';
import { BASE_URL } from './testUtils';

describe('PandaDoc Logs API', () => {
  it('returns empty logs array when no logs exist', async () => {
    const response = await SELF.fetch(`${BASE_URL}/pandadoc/logs`);
    
    expect(response.status).toBe(200);
    
    const data = await response.json();
    expect(data).toHaveProperty('logs');
    expect(data).toHaveProperty('count');
    expect(data).toHaveProperty('limit');
    expect(Array.isArray(data.logs)).toBe(true);
    expect(data.count).toBe(data.logs.length);
    expect(data.limit).toBe(50); // default limit
  });

  it('respects custom limit parameter', async () => {
    const response = await SELF.fetch(`${BASE_URL}/pandadoc/logs?limit=10`);
    
    expect(response.status).toBe(200);
    
    const data = await response.json();
    expect(data.limit).toBe(10);
  });

  it('enforces maximum limit of 200', async () => {
    const response = await SELF.fetch(`${BASE_URL}/pandadoc/logs?limit=500`);
    
    expect(response.status).toBe(200);
    
    const data = await response.json();
    expect(data.limit).toBe(200); // should be capped at 200
  });
});
